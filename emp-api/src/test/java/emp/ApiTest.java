package emp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;

import java.io.IOException;
import java.security.MessageDigest;
import java.util.concurrent.TimeUnit;

public class ApiTest {

  static boolean v1 = true; // true使用老版本的认证，false新用auth center的认证
//  static String url = "http://app-langchao-cloud.net4iot.com:16002"; // emp-api url
//  static String auth_center_url = "http://auth-langchao-cloud.net4iot.com:16004"; // auth
   static String auth_center_url = "http://linkos-auth-center-out.test.service.easylinkin.net";
   static String url = "http://localhost:8080";//"http://************:15012";
//   static String url = "http://linkthings-api-out.dev.service.easylinkin.net";
  // 旧版本认证
  static String v1_apiSecret = "22edd3274a7c4fdebcbc99bcd4cd6a73";
  static String v1_app_key = "093bf019bf974481a0b4be7f25eeaccb";
  // Auth center认证
  static String v2_clientId = "5d1a2262d9f94ebe95ce86fcb79d285d";
  static String v2_appId = "2020050900000002";
  static String v2_client_secret =
      "$2a$10$J4eoEtDTHbWOwVPry/uoqOzylBLbH51Eg7SLla9rQqLE9fRM0SVpy";

  public static void main(String[] args) {
    // getToken();
//     deviceListAll();
//     deviceFlowList();
    deviceFlowListIn10Days();
//     getDevicesByProject("1");
//     communityList();
//     project_Add();
//     project_updateProject();
//     project_deleteProject();
//     project_getProjectById();
//     project_getProjectForCurrentTenantList();
//     communit_addCommunit();
//     communit_updateCommunit();
//     deviceUnit_getById();
//     deviceUnit_getDeviceUnitWithDeviceModel();
//     deviceUnit_getDeviceUnitListForCurrentTenant();
//     deviceUnit_getDeviceUnitByProject("5");
//     deviceType_getList();
//     device_add();
//     device_update();
//     device_list();
//     device_byCode();
//     communit_deleteCommunit();
//     getTenantInfo();
//     updateTenantAccount();
//    getDevicesByProject("5");
//     findActiveAppList();
//    deviceInstall();
//    device_api_down();
  }

  private static void deviceFlowListIn10Days() {
    OkHttpClient client = new OkHttpClient().newBuilder().readTimeout(30,TimeUnit.SECONDS).build();

    MediaType mediaType = MediaType.parse("application/json");
    RequestBody body = RequestBody.create(mediaType, "{\n" +
            "    \"sort\": {},\n" +
            "    \"model\": {\n" +
            "        \"deviceName\": \"\",\n" +
            "        \"startTime\": \"2023-12-31 10:44:27\",//必填，开始结束时间不能跨月\n" +
            "        \"endTime\": \"2024-01-09 23:59:59\"//必填，开始结束时间不能跨月\n" +
//            "        \"deviceCodes\": \"VIR202310271754\",//必填\n" +
//            "        \"deviceTypeName\": \"多合一水质监测器\",\n" +
//            "        \"deviceUnitCode\": \"SZJC-4G-01-0202\"\n" +
            "    }\n" +
            "}");

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/deviceFlow/v1/listIn10Days").method("POST", body);
    addHeader(builder);

    Request request = builder.addHeader("Content-Type", "application/json").build();
    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  static void device_api_down() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    Request.Builder builder = new Request.Builder();
    MediaType mediaType = MediaType.parse("application/json");
    RequestBody body = RequestBody.create(mediaType, "{\"confirm\":1,\"device_id\":\"00137A100000CCAC\",\"parameter\":{\"alarm_switch\":\"0\",\"alarm_duration_time\":10,\"strobe_mode\":\"01\",\"alarm_mode\":\"00\"},\"service_id\":\"alarm_control\"}");
    builder.url(url + "/api/down").method("POST", body);
    addHeader(builder);
    Request request = builder.build();
    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  private static void deviceInstall() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    Request.Builder builder = new Request.Builder();
    MediaType mediaType = MediaType.parse("application/json");
    RequestBody body = RequestBody.create(mediaType, "{\"code\":\"004A77012400CB63\",\"latitude\":\"30.500203\",\"location\":\"力兴电源:生产房三楼\",\"longitude\":\"114.434027\",\"name\":\"生产三楼\",\"remark\":\"\",\"spaceId\":\"4b99c5c6fd6373632b068ee7ef609fd5\"}");
    builder.url(url + "/api/device/v1/deviceInstall").method("POST", body);
    addHeader(builder);
    Request request = builder.build();
    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  static String token = null;

  /**
   * 获取token
   */
  static String getToken(String clientId, String client_secret) {
    if (null != token) {
      return token;
    }
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
    RequestBody body = RequestBody.create(mediaType, "client_id=" + clientId + "&client_secret="
        + client_secret + "&grant_type=client_credentials&scope=read write");
    Request request = new Request.Builder() //
        .url(auth_center_url + "/oauth/token").method("POST", body)
        .addHeader("Content-Type", "application/x-www-form-urlencoded")//
        // .addHeader("Cookie", "JSESSIONID=8E00E29CED2CAB8B16A504DDB2E75D3D")
        .build();
    try {
      Response response = client.newCall(request).execute();
      String string = response.body().string();
      JSONObject jsonObject = JSON.parseObject(string);
      System.out.println("getToken >> " + jsonObject.getString("access_token"));
      token = jsonObject.getString("access_token");
      return token;
    } catch (IOException e) {
      e.printStackTrace();
    }
    return "";
  }

  static Request.Builder addHeader(Request.Builder builder) {
    if (v1) {
      String nonce = (int) System.currentTimeMillis() + "";
      String timestamp = String.valueOf(System.currentTimeMillis());

      builder.addHeader("api-key", v1_app_key) //
          .addHeader("nonce", nonce) //
          .addHeader("timestamp", timestamp) //
          .addHeader("signature", getSha1(nonce + timestamp + v1_apiSecret)); //
//          .addHeader("tenantId", "20000589");
    } else {
      builder //
//          .addHeader("appId", "2020050900000002") //
          .addHeader("appKey", v2_clientId) //
          .addHeader("appId", v2_appId) //
          .addHeader("accessToken", "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************") //
          .addHeader("timestamp", String.valueOf(System.currentTimeMillis())) //
          .addHeader("tenantId", "20000458");
    }
    return builder;
  }

  /**
   * 查询设备列表
   */
  static void deviceListAll() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/device/v1/listAll").method("GET", null);
    addHeader(builder);
    Request request = builder.build();
    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 流水列表
   */
  static void deviceFlowList() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    MediaType mediaType = MediaType.parse("application/json");
    RequestBody body = RequestBody.create(mediaType, "{\n " //
        + "\"start_time\": \"1572075682000\"," //
        + "\"end_time\":\"\", " //
        + "\"page\": \"0\"," //
        + "\"size\":\"10\"\n" //
        + "}");

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/deviceFlow/v1/list").method("POST", body);
    addHeader(builder);

    Request request = builder.addHeader("Content-Type", "application/json").build();
    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 查询分组数据
   */
  static void communityList() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    MediaType mediaType = MediaType.parse("application/json");
    RequestBody body = RequestBody.create(mediaType,
        "{\r\n\t\"model\": {},\r\n\"pageable\":{\"page\": 0,\"size\":15}\r\n}");

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/community/list").method("POST", body);
    addHeader(builder);

    Request request = builder.addHeader("Content-Type", "application/json").build();
    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 新增项目
   */
  static void project_Add() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    MediaType mediaType = MediaType.parse("application/json");
    RequestBody body = RequestBody.create(mediaType, "{\r\n    " //
        + "\"name\": \"测试项目abcd\",\r\n    " //
        + "\"remark\": \"备注\",\r\n    " //
        + "\"industryType\": \"1\",\r\n   " //
        + " \"projectNo\": \"12345678\",\r\n    " //
        + "\"projectDesc\": \"项目说明\"\r\n}");

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/project/v1/add").method("POST", body);
    addHeader(builder);

    Request request = builder.addHeader("Content-Type", "application/json").build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 更新项目
   */
  static void project_updateProject() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    MediaType mediaType = MediaType.parse("application/json");
    RequestBody body =
        RequestBody.create(mediaType, "{\"id\": 20000981,\"name\":\"测试项目ab\",\"remark\":\"备注\"}");

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/project/v1/update").method("POST", body);
    addHeader(builder).addHeader("Content-Type", "application/json");

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 删除项目
   */
  static void project_deleteProject() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
    RequestBody body = RequestBody.create(mediaType, "id=20000831");

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/project/v1/delete").method("POST", body);
    addHeader(builder).addHeader("Content-Type", "application/x-www-form-urlencoded");

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 根据id查项目
   */
  static void project_getProjectById() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/project/v1/getProject?id=20000969").method("GET", null);
    addHeader(builder);

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 当前租户下项目列表
   */
  static void project_getProjectForCurrentTenantList() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/project/v1/getProjectForCurrentTenantList").method("GET", null);
    addHeader(builder);

    Request request = builder.build();
    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 添加分组
   */
  static void communit_addCommunit() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    MediaType mediaType = MediaType.parse("application/json");
    RequestBody body = RequestBody.create(mediaType,
        "{\n\t\"project_id\":\"20000298\",\n\t\"group_name\":\"测试添加分组\"\n}");

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/project/v1/addCommunit").method("POST", body);
    addHeader(builder).addHeader("Content-Type", "application/json");

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 修改分组
   */
  static void communit_updateCommunit() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    MediaType mediaType = MediaType.parse("application/json");
    RequestBody body = RequestBody.create(mediaType,
        "{\n\t\"group_id\":\"3004763\",\n\t\"group_name\":\"测试修改\"\n}");

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/project/v1/updateCommunit").method("POST", body);
    addHeader(builder).addHeader("Content-Type", "application/json");

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 设备型号-根据型号编号查询
   */
  static void deviceUnit_getById() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/deviceUnit/v1/getDeviceUnit?deviceUnitCode=HW-470AA1-01-0016")
        .method("GET", null);
    addHeader(builder);

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 设备型号-查询当前租户的设备型号
   */
  static void deviceUnit_getDeviceUnitListForCurrentTenant() {
    OkHttpClient client = new OkHttpClient().newBuilder().connectTimeout(10, TimeUnit.SECONDS)// 设置连接超时时间
        .readTimeout(20, TimeUnit.SECONDS)// 设置读取超时时间
        .build();
    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/deviceUnit/v1/getDeviceUnitListForCurrentTenant").method("GET", null);
    addHeader(builder);

    Request request = builder.build();
    try {
      Response response = client.newCall(request).execute();
      System.out.println("deviceUnit_getDeviceUnitListForCurrentTenant >> " + response);
      System.out
          .println("deviceUnit_getDeviceUnitListForCurrentTenant >> " + response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 获取设备类型列表
   */
  static void deviceType_getList() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/deviceType/v1/getDeviceTypeList").method("GET", null);
    addHeader(builder);

    Request request = builder.build();
    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 设备-添加
   */
  static void device_add() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    MediaType mediaType = MediaType.parse("application/json");
    RequestBody body = RequestBody.create(mediaType,
        "{\n    " + "\"device_id\": \"004A770124000018\",\n    "//
            + "\"name\": \"门锁1\",\n    " //
            + "\"device_unit\": \"MC-470AA1-02-0016\",\n    " //
            + "\"project_name\": \"copy\",\n    " //
            + "\"group_name\": \"测试修改\",\n    " //
            + "\"imsi\": \"\"\n}");

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/device/v1/add").method("POST", body);
    addHeader(builder).addHeader("Content-Type", "application/json");

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 设备-修改
   */
  static void device_update() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    MediaType mediaType = MediaType.parse("application/json");
    RequestBody body = RequestBody.create(mediaType, "{\r\n    " //
        + "\"device_id\": \"004A770124000028\",\r\n    " //
        + "\"name\": \"门锁1update\",\r\n    " //
        + "\"project_name\": \"copy\",\r\n    " //
        + "\"group_name\": \"测试修改\",\r\n    " //
        + "\"imsi\": \"0102030405060708\"\r\n" //
        + "}");

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/device/v1/update").method("POST", body);
    addHeader(builder).addHeader("Content-Type", "application/json");

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 设备-列表
   */
  static void device_list() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/device/v1/listAll").method("GET", null);
    addHeader(builder);

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 根据设备编号查询
   */
  static void device_byCode() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/device/v1/list/2021031300540001").method("GET", null);
    addHeader(builder);

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 删除分组
   */
  static void communit_deleteCommunit() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
    RequestBody body = RequestBody.create(mediaType, "group_id=3004721");

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/project/v1/deleteCommunit").method("POST", body);
    addHeader(builder).addHeader("Content-Type", "application/x-www-form-urlencoded");

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 获取当前租户信息
   */
  static void getTenantInfo() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/user/getTenantInfo").method("GET", null);
    addHeader(builder);

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 获取设备型号列表包含所有设备型号列表
   */
  static void deviceUnit_getDeviceUnitWithDeviceModel() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/deviceUnit/v1/getDeviceUnitWithDeviceModel").method("GET", null);
    addHeader(builder);

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 修改租户账号
   */
  static void updateTenantAccount() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    MediaType mediaType = MediaType.parse("application/json");
    RequestBody body = RequestBody.create(mediaType,
        "{\r\n    \"account\":\"admin@69683\",\r\n    \"newAccount\":\"accountName\"\r\n}");

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/appBuyList/updateAccount").method("POST", body);
    addHeader(builder).addHeader("Content-Type", "application/json");

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 根据设备查询设备型号
   */
  static void deviceUnit_getDeviceUnitByProject(String projectId) {
    OkHttpClient client = new OkHttpClient().newBuilder().build();

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/deviceUnit/v1/getDeviceUnitByProject?id="+ projectId).method("GET", null);
    addHeader(builder);

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println("deviceUnit_getDeviceUnitByProject >> " + response);
      System.out.println("deviceUnit_getDeviceUnitByProject >> " + response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 根据项目查询设备列表
   */
  static void getDevicesByProject(String projectId) {
    OkHttpClient client = new OkHttpClient().newBuilder().build();

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/device/v1/getDevicesByProject?id=" + projectId+"&page=1&pageSize=10").method("GET", null);
    addHeader(builder).addHeader("Content-Type", "application/json");

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println("getDevicesByProject >> " + response.message() + " " + response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 修改租户账号
   */
  static void findActiveAppList() {
    OkHttpClient client = new OkHttpClient().newBuilder().build();
    MediaType mediaType = MediaType.parse("application/json");
    RequestBody body = RequestBody.create(mediaType,
        "[\n" + "    \"02ae07eb1ca34c7ba18e0381f7d9227520000023\",\n"
            + "    \"02ae07eb1ca34c7ba18e0381f7d9227520000026\",\n"
            + "    \"02ae07eb1ca34c7ba18e0381f7d9227520000027\",\n"
            + "    \"02ae07eb1ca34c7ba18e0381f7d9227520000028\",\n"
            + "    \"02ae07eb1ca34c7ba18e0381f7d9227520000029\",\n"
            + "    \"02ae07eb1ca34c7ba18e0381f7d9227520000035\",\n"
            + "    \"02ae07eb1ca34c7ba18e0381f7d9227520000036\",\n"
            + "    \"02ae07eb1ca34c7ba18e0381f7d9227520000037\",\n"
            + "    \"02ae07eb1ca34c7ba18e0381f7d9227520000038\",\n"
            + "    \"02ae07eb1ca34c7ba18e0381f7d9227520000039\",\n"
            + "    \"02ae07eb1ca34c7ba18e0381f7d9227520000043\"\n" + "]");

    Request.Builder builder = new Request.Builder();
    builder.url(url + "/api/appBuyList/findActiveAppList").method("POST", body);
    addHeader(builder).addHeader("Content-Type", "application/json");

    Request request = builder.build();

    try {
      Response response = client.newCall(request).execute();
      System.out.println(response.body().string());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  public static String getSha1(String str) {

    char hexDigits[] =
        {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
    try {
      MessageDigest mdTemp = MessageDigest.getInstance("SHA1");
      mdTemp.update(str.getBytes("UTF-8"));
      byte[] md = mdTemp.digest();
      int j = md.length;
      char buf[] = new char[j * 2];
      int k = 0;
      for (int i = 0; i < j; i++) {
        byte byte0 = md[i];
        buf[k++] = hexDigits[byte0 >>> 4 & 0xf];
        buf[k++] = hexDigits[byte0 & 0xf];
      }
      return new String(buf);
    } catch (Exception e) {
      return null;
    }
  }
}
