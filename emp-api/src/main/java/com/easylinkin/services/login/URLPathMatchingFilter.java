package com.easylinkin.services.login;

import java.util.Arrays;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.shiro.web.filter.PathMatchingFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import com.easylinkin.dreambase.util.ApiAccessResult;
import com.easylinkin.dreambase.util.ApiAccessService;
import com.easylinkin.services.oauth.constants.AuthApiConstant;
import com.easylinkin.services.oauth.service.OauthService;
import com.easylinkin.services.user.service.UserService;


/**
 * 全局拦截器
 *
 * @Data 20191112
 * <AUTHOR> LIUYQ
 */
public class URLPathMatchingFilter extends PathMatchingFilter {

  private static final String[] headersDic = {"api-key", "nonce", "timestamp", "signature"};

  // private static final String[] headersDicV2 = {AuthApiConstant.Header.APP_KEY,
  // AuthApiConstant.Header.ACCESS_TOKEN, AuthApiConstant.Header.TIMESTAMP};

  private static final Logger log = LoggerFactory.getLogger(URLPathMatchingFilter.class);

  private static String nonceGen() {
    String res = "";
    for (int i = 0; i < 8; i++) {
      res += (int) (Math.random() * 10);
    }
    return res;
  }

  @Autowired
  private UserService userService;

  @Autowired
  private OauthService oauthService;

  /**
   * 访问controller 之前
   * 
   * @param request
   * @param response
   * @return
   * @throws Exception
   */
  @Override
  protected boolean onPreHandle(ServletRequest request, ServletResponse response,
      Object mappedValue) throws Exception {

    HttpServletRequest req = (HttpServletRequest) request;
    if ("Options".equalsIgnoreCase(req.getMethod())) {
      return true;
    }
    HttpServletResponse res = (HttpServletResponse) response;
    String path = req.getServletPath();
    log.info("数据签名认证开始,url={}", path);
    Enumeration<String> enu = req.getHeaderNames();
    List<String> headers = Collections.list(enu);
    /********** 新的认证方式20200309 添加 by zys V2 ***********************/
    if (!StringUtils.isEmpty(req.getHeader(AuthApiConstant.Header.APP_KEY))
        && !StringUtils.isEmpty(req.getHeader(AuthApiConstant.Header.ACCESS_TOKEN))
        && !StringUtils.isEmpty(req.getHeader(AuthApiConstant.Header.TIMESTAMP))) {
      try {
        if (StringUtils.isEmpty(req.getHeader(AuthApiConstant.Header.TENANTID))) {
          String resStr = "{\"result\": " + AuthApiConstant.RsponseCode.ERROR_500
              + ",\"message\":\"" + "tenantId is empty on Header" + "\"}";
          res.getWriter().write(resStr);
          return false;
        }
        String appKey = req.getHeader(AuthApiConstant.Header.APP_KEY);
        String accessToken = req.getHeader(AuthApiConstant.Header.ACCESS_TOKEN);
        String timestamp = req.getHeader(AuthApiConstant.Header.TIMESTAMP);
        String tenantId = req.getHeader(AuthApiConstant.Header.TENANTID);
        int icode = oauthService.checkToken(appKey, accessToken, Long.parseLong(timestamp));
        if (icode == AuthApiConstant.RsponseCode.TOKEN_OK) {
          return true;
        }
        if (icode == AuthApiConstant.RsponseCode.ERROR_501) {
          log.warn(appKey + tenantId + ":" + "token:" + accessToken + ": auth fail");
          String resStr =
              "{\"result\": " + AuthApiConstant.RsponseCode.ERROR_501 + ",\"message\":\"" + "租户ID:"
                  + tenantId + ",token:" + accessToken + ":auth fail" + "\"}";
          res.getWriter().write(resStr);
          return false;
        }
        if (icode == AuthApiConstant.RsponseCode.TIME_OUT) {
          log.warn(appKey + ":接口请求超时.....");
          String resStr = "{\"result\": " + AuthApiConstant.RsponseCode.TIME_OUT + ",\"message\":\""
              + "request timeout" + "\"}";
          res.getWriter().write(resStr);
          return false;
        }
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        String resStr = "{\"result\": " + AuthApiConstant.RsponseCode.ERROR_500 + ",\"message\":\""
            + "param validation fail: " + e.getMessage() + "\"}";
        res.getWriter().write(resStr);
        return false;
      }

    }

    /******************** 下面为老的授权认证方式20200309之前-V1 ****************************************/
    // 判断请求头是否合法
    if (headers.containsAll(Arrays.asList(headersDic))) {
      String apiKey = req.getHeader("api-key");
      String nonce = req.getHeader("nonce");
      String timestamp = req.getHeader("timestamp");
      String signature = req.getHeader("signature");
      String apiSecret = userService.findApiSecretByApiKey(apiKey);
      if (StringUtils.isEmpty(apiSecret)) {
        res.setStatus(400);
        String resStr = "{\"result\": false,\"message\":\"api-key incorrect\"}";
        res.getWriter().write(resStr);
        return false;
      }
      request.setAttribute("apiSecret", apiSecret);
      // 调用core鉴权方法
      ApiAccessResult result = ApiAccessService.isAccess(nonce, timestamp, signature, apiSecret);
      if (result.getResult()) {
        return true;
      } else {
//        log.warn("api鉴权失败，失败原因：" + result.getDes());
//        res.setStatus(401);
//        String resStr =
//            "{\"result\": " + result.getResult() + ",\"message\":\"" + result.getDes() + "\"}";
//        res.getWriter().write(resStr);
        return true;
      }
    } else {
//      log.info("需要鉴权的接口请求不合法");
//      res.setStatus(401);
//      String resStr = "{\"result\": false,\"message\":\"param is Illegal\"}";
//      res.getWriter().write(resStr);
      return true;
    }
  }

  @Override
  protected void postHandle(ServletRequest request, ServletResponse response) throws Exception {
    HttpServletRequest req = (HttpServletRequest) request;
    HttpServletResponse res = (HttpServletResponse) response;
    // if (!"OPTIONS".equals(httpServletRequest.getMethod())) { V1
    if (!"OPTIONS".equals(req.getMethod())
        && StringUtils.isEmpty(req.getHeader(AuthApiConstant.Header.ACCESS_TOKEN))) { // V2
                                                                                      // zys
      Object attribute = request.getAttribute("apiSecret");
      if (attribute != null) {
        String nonce = nonceGen();
        String timestamp = System.currentTimeMillis() + "";
        String apiSecret = attribute.toString();
        String signature = ApiAccessService.getSha1(nonce + timestamp + apiSecret);
        res.addHeader("nonce", nonce);
        res.addHeader("timestamp", timestamp);
        res.addHeader("signature", signature);
      }
    } else {
      res.addHeader("Access-Control-Allow-Headers",
          "nonce,access-control-allow-origin,timestamp,signature,api-key,appKey,accessToken,projectId");
    }
    super.postHandle(request, response);
  }
}

