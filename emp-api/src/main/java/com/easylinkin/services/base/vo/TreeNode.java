package com.easylinkin.services.base.vo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 封装 node 树节点属性bean
 */
public class TreeNode
{
    
    /**
     * 当前节点id
     */
    private Long id;
    
    /**
     * 是否选中
     */
    private Boolean checked;
    
    /**
     * 是否置灰  true 表示此节点的 checkbox / radio 被禁用。false 表示此节点的 checkbox / radio 可以使用。
     */
    private Boolean chkDisabled;
    
    /**
     * 绑定单击事件  click:"alert('I can not jump...');"
     */
    private String click;
    
    /**
     * 强制节点的 checkBox / radio 的 半勾选状态
     */
    private String halfCheck;
    
    /**
     * 自定义打开和关闭时的图片
     */
    private String icon;
    
    /**
     * 自定义打开时显示的图片
     */
    private String iconOpen;
    
    /**
     * 自定义关闭时显示的图片
     */
    private String iconClose;
    
    /**
     * 设置个性图标的 className
     */
    private String iconSkin;
    
    /**
     * 父节点id
     */
    private Long pId;
    
    /**
     * 节点名称
     */
    private String name;
    
    /**
     * 节点名称
     */
    private Boolean open;
    
    /**
     * 是否为父节点
     */
    private Boolean isParent;
    
    /**
     *  判断 treeNode 节点是否被隐藏。
     */
    private Boolean isHidden;
    
    /**
     * 是否进行异步加载
     */
    private Boolean isAjaxing;
    
    /**
     * 记录 treeNode 节点是否已经进行过异步加载,避免父节点反复异步加载数据。
     */
    private Boolean zasync;
    
    /**
     * 字体   例：font:{'font-weight':'bold'}
     */
    private String font;
    
    /**
     * 点击链接地址  url:"http://www.baby666.cn"
     */
    private String url;
    
    /**
     * 链接目标      target:"_blank"
     */
    private String target;
    
    /**
     * 是否可以折叠
     */
    private Boolean collapse;
    
    /**
     *是否可以展开
     */
    private Boolean expand;
    
    /**
     * 设置节点是否隐藏 checkbox / radio [setting.check.enable = true 时有效]
     */
    private Boolean nocheck;
    
    /**
     * 是否可以操作
     */
    private Boolean doCheck;
    
    /**
     * 是否可拖拽
     */
    private Boolean drag;
    
    /**
     * 不能拖拽到父节点以外
     */
    private Boolean dropInner;
    
    /**
     *     是否可以拖拽到子节点下
     */
    private Boolean childOuter;
    
    /**
     *     联动计量单位
     */
    private Integer isCheck;
    
    /**
     *     联动是否盘点
     */
    private Integer level;
    
    /**
     * 节点类型
     */
    private Integer type;
    
    /**
     * 自定义属性
     */
    private Map<String, String> attrMap = new HashMap<String, String>();
    
    private List<TreeNode> children;
    
    public Map<String, String> getAttrMap()
    {
        return attrMap;
    }
    
    public void setAttrMap(Map<String, String> attrMap)
    {
        this.attrMap = attrMap;
    }
    
    public Long getId()
    {
        return id;
    }
    
    public void setId(Long id)
    {
        this.id = id;
    }
    
    public Boolean getChecked()
    {
        return checked;
    }
    
    public void setChecked(Boolean checked)
    {
        this.checked = checked;
    }
    
    public Boolean getChkDisabled()
    {
        return chkDisabled;
    }
    
    public void setChkDisabled(Boolean chkDisabled)
    {
        this.chkDisabled = chkDisabled;
    }
    
    public String getClick()
    {
        return click;
    }
    
    public void setClick(String click)
    {
        this.click = click;
    }
    
    public String getHalfCheck()
    {
        return halfCheck;
    }
    
    public void setHalfCheck(String halfCheck)
    {
        this.halfCheck = halfCheck;
    }
    
    public String getIcon()
    {
        return icon;
    }
    
    public void setIcon(String icon)
    {
        this.icon = icon;
    }
    
    public String getIconOpen()
    {
        return iconOpen;
    }
    
    public void setIconOpen(String iconOpen)
    {
        this.iconOpen = iconOpen;
    }
    
    public String getIconClose()
    {
        return iconClose;
    }
    
    public void setIconClose(String iconClose)
    {
        this.iconClose = iconClose;
    }
    
    public String getIconSkin()
    {
        return iconSkin;
    }
    
    public void setIconSkin(String iconSkin)
    {
        this.iconSkin = iconSkin;
    }
    
    public Long getpId()
    {
        return pId;
    }
    
    public void setpId(Long pId)
    {
        this.pId = pId;
    }
    
    public String getName()
    {
        return name;
    }
    
    public void setName(String name)
    {
        this.name = name;
    }
    
    public Boolean getOpen()
    {
        return open;
    }
    
    public void setOpen(Boolean open)
    {
        this.open = open;
    }
    
    public Boolean getIsParent()
    {
        return isParent;
    }
    
    public void setIsParent(Boolean isParent)
    {
        this.isParent = isParent;
    }
    
    public Boolean getIsHidden()
    {
        return isHidden;
    }
    
    public void setIsHidden(Boolean isHidden)
    {
        this.isHidden = isHidden;
    }
    
    public Boolean getIsAjaxing()
    {
        return isAjaxing;
    }
    
    public void setIsAjaxing(Boolean isAjaxing)
    {
        this.isAjaxing = isAjaxing;
    }
    
    public Boolean getZasync()
    {
        return zasync;
    }
    
    public void setZasync(Boolean zasync)
    {
        this.zasync = zasync;
    }
    
    public String getFont()
    {
        return font;
    }
    
    public void setFont(String font)
    {
        this.font = font;
    }
    
    public String getUrl()
    {
        return url;
    }
    
    public void setUrl(String url)
    {
        this.url = url;
    }
    
    public String getTarget()
    {
        return target;
    }
    
    public void setTarget(String target)
    {
        this.target = target;
    }
    
    public Boolean getCollapse()
    {
        return collapse;
    }
    
    public void setCollapse(Boolean collapse)
    {
        this.collapse = collapse;
    }
    
    public Boolean getExpand()
    {
        return expand;
    }
    
    public void setExpand(Boolean expand)
    {
        this.expand = expand;
    }
    
    public Boolean getNocheck()
    {
        return nocheck;
    }
    
    public void setNocheck(Boolean nocheck)
    {
        this.nocheck = nocheck;
    }
    
    public Boolean getDoCheck()
    {
        return doCheck;
    }
    
    public void setDoCheck(Boolean doCheck)
    {
        this.doCheck = doCheck;
    }
    
    public Boolean getDrag()
    {
        return drag;
    }
    
    public void setDrag(Boolean drag)
    {
        this.drag = drag;
    }
    
    public Boolean getDropInner()
    {
        return dropInner;
    }
    
    public void setDropInner(Boolean dropInner)
    {
        this.dropInner = dropInner;
    }
    
    public Boolean getChildOuter()
    {
        return childOuter;
    }
    
    public void setChildOuter(Boolean childOuter)
    {
        this.childOuter = childOuter;
    }
    
    public Integer getIsCheck()
    {
        return isCheck;
    }
    
    public void setIsCheck(Integer isCheck)
    {
        this.isCheck = isCheck;
    }
    
    public Integer getLevel()
    {
        return level;
    }
    
    public void setLevel(Integer level)
    {
        this.level = level;
    }
    
    public Integer getType()
    {
        return type;
    }
    
    public void setType(Integer type)
    {
        this.type = type;
    }
    
    public List<TreeNode> getChildren()
    {
        return children;
    }
    
    public void setChildren(List<TreeNode> children)
    {
        this.children = children;
    }
    
}
