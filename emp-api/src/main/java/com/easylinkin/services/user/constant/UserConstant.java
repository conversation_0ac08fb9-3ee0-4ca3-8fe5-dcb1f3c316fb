package com.easylinkin.services.user.constant;

/**
 * 用户常量
 *
 * <AUTHOR>
 * @version [1.0.0, 2018/7/31] 
 * @since 1.0-SNAPSHOT
 */
public interface UserConstant
{
    /**
     * 模块编号
     *
     * @apiNote 注意和权限表保持一致
     */
    interface ModuleId
    {
        /**
         * 用户模块编号
         */
        String USER = "40";
        String MODIFY_SELF_INFO ="-1011";
    }
    
    interface MessageCode
    {
        /**
         * 新增用户
         */
        String USER_ADD = "log.user.add"; 
        
        /**
         * 更新用户
         */
        String USER_UPDATE = "log.user.update"; 
        
        /**
         * 删除用户
         */
        String USER_DELETE = "log.user.delete";
        
        //重置其它用户密码
        String USER_RESET_PWD = "log.user.resetPWD";
        
        //修改主题
        String USER_THEME = "log.user.theme";
        
        //用户解锁
        String USER_UNLOCK = "log.user.unlock";
        
        //用户上传图片
        String USER_IMAGE = "log.user.updateImage";

        String INITIAL_PWD = "easylinkin@123";
    }

    interface ExceptionCode {
        //用户已经存在
        String ACCOUNT_REPEAT = "10000";
        /**
         * 记录已删除
         */
        String RECORD_DELETE = "00001";
        /**
         * 用户被锁定
         */
        String USER_LOCKED = "user.exception.user_locked";
        /**
         * 用户名或密码错误
         */
        String USERNAME_OR_PASSWORD_INCORRECT = "user.username_or_paw_incorrect";
        /**
         * 批量导入，表格数据有误
         */
        String BATCHIMPORT_EXECL_DATA_ERROR = "BATCHIMPORT_EXECL_DATA_ERROR";
        
        /**
         * 
         */
        String BATCHIMPORT_NO_MACTCH_ROLE = "BATCHIMPORT_NO_MACTCH_ROLE";
        /**
         * 批量导入，必填字段为空了
         */
        String BATCHIMPORT_REQUIRED_EMPTY = "BATCHIMPORT_REQUIRED_EMPTY";
        /**
         * 批量导入，内容为空了
         */
        String BATCHIMPORT_FILE_EMPTY = "BATCHIMPORT_FILE_EMPTY";
        /**
         * 批量导入，用户模板错误
         */
        String BATCHIMPORT_USER_FILE_TEMP_ERROR = "BATCHIMPORT_USER_FILE_TEMP_ERROR";
        /**
         * 批量导入，找不到匹配公司
         */
        String BATCHIMPORT_NO_MATCH_COMPANY = "BATCHIMPORT_NO_MATCH_COMPANY";
        /**
         * 如果是普通用户，没有权限导入其他公司的用户
         */
        String BATCHIMPORT_NO_AUTH_IMPORT_OTHER_COMPANY = "BATCHIMPORT_NO_AUTH_IMPORT_OTHER_COMPANY";
        /**
         * 手机号码格式不正确
         */
        String TELEPHONE_ERROR = "TELEPHONE_ERROR";
        /**
         * 传真格式不正确
         */
        String FAX_ERROR = "FAX_ERROR";
        /**
         * 固定电话格式不正确
         */
        String FIXED_TELEPHONE_ERROR = "FIXED_TELEPHONE_ERROR";
        /**
         * 邮箱格式不正确
         */
        String EMAIL_ERROR = "EMAIL_ERROR";
        /**
         * 没有需要导出的数据
         */
        String NO_USERS_EXPORT = "user.10001";
        /**
         * 第{0}行账号重复
         */
        String ACCOUNT_REPEAT_IN_NUM = "ACCOUNT_REPEAT_IN_NUM";
        /**
         * 第{0}行手机号错误
         */
        String TELEPHONE_ERROR_IN_NUM = "TELEPHONE_ERROR_IN_NUM";
        /**
         * 第{0}行传真错误
         */
        String FAX_ERROR_IN_NUM = "FAX_ERROR_IN_NUM";
        /**
         * 第{0}行邮箱错误
         */
        String EMAIL_ERROR_IN_NUM = "EMAIL_ERROR_IN_NUM";
        /**
         * 第{0}行固定电话错误
         */
        String FIXED_TELEPHONE_ERROR_IN_NUM = "FIXED_TELEPHONE_ERROR_IN_NUM";
        /**
         * 第{0}行用户不存在
         */
        String USER_NO_EXIST_IN_NUM = "USER_NO_EXIST_IN_NUM";
        
        /**
         * 
         */
        String USER_OPERATER_NO_EXIST_IN_NUM = "USER_OPERATER_NO_EXIST_IN_NUM";
        String USER_MANAGER_NO_EXIST_IN_NUM = "";
        
        String LOST_CURRENT_USER = "LOST_CURRENT_USER";
        /**
         * 用户不存在
         */
        String USER_NO_FOUND = "USER_NO_FOUND";
        
    }
    interface StatusCode
    {
        /**
         * 解锁状态
         */
        Integer UNLOCK_STATUS = 0;
        /**
         * 锁状态
         */
        Integer LOCK_STATUS = 1;
    }
}
