package com.easylinkin.emp.device.repository;

import com.easylinkin.emp.address.entity.Community;
import com.easylinkin.emp.device.entity.Device;
import com.easylinkin.emp.device.entity.DeviceUnit;
import com.easylinkin.services.data.repository.JpaDao;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * 设备数据访问对象
 *
 * <AUTHOR> // 作者
 * @version [1.0.0, 2018/7/10] // 版本、时间
 * @see JpaDao // 相关类
 * @since 1.0-SNAPSHOT // 产品/模块版本
 */
public interface DeviceDao extends JpaDao<Device, String> {



//    /**
//     * 近一个月按天统计数量
//     * <br>
//     *
//     * @return
//     * <AUTHOR>
//     * @see [类、类#方法、类#成员]
//     */
//    @Query(value = "SELECT t1.day, ( SELECT SUM(t2.number) FROM "
//            + "        (SELECT DAY(e.create_time)  day, count(*)  number  FROM emp_device e where (YEAR(:create_time)*12+MONTH(:create_time))-(YEAR(e.create_time)*12+MONTH(e.create_time))=0 GROUP BY DAY(e.create_time) ORDER BY DAY(e.create_time))  t2"
//            + "                 WHERE t2.day <= t1.day ) + (SELECT COUNT(*) FROM emp_device e WHERE (YEAR(:create_time)*12+MONTH(:create_time))-(YEAR(e.create_time)*12+MONTH(e.create_time))>0) FROM "
//            + "        (SELECT DAY(e.create_time)  day, count(*)  number  FROM emp_device e where (YEAR(:create_time)*12+MONTH(:create_time))-(YEAR(e.create_time)*12+MONTH(e.create_time))=0 GROUP BY DAY(e.create_time) ORDER BY DAY(e.create_time))  t1 ", nativeQuery = true)
//    List<String> findDeviceByMonthAdmin(@Param("create_time") Date create_time);


    /**
     * 近一个月按天统计数量
     * <br>
     *
     * @return
     * <AUTHOR>
     * @see [类、类#方法、类#成员]
     */
    @Query(value = " SELECT DAY(create_time ) AS create_day, " +
            " count(*) + " +
            "(SELECT count(*)  FROM emp_device WHERE DAY ( create_time ) < create_day and (YEAR (:create_time )* 12 + MONTH (:create_time ))-(YEAR (create_time )* 12 + MONTH (create_time ))= 0 ) + " +
            " (SELECT count(*)  FROM emp_device  WHERE (YEAR (:create_time )* 12 + MONTH (:create_time ))-(YEAR (create_time )* 12 + MONTH (create_time ))> 0 ) " +
            " FROM " +
            " emp_device " +
            " WHERE " +
            " YEAR ( create_time ) = YEAR (:create_time ) " +
            " AND MONTH ( create_time ) = MONTH (:create_time ) " +
            " GROUP BY create_day " +
            " ORDER BY create_day ", nativeQuery = true)
    List<Object[]> findDeviceByMonthAdmin(@Param("create_time") Date create_time);


    /**
     * 近一年按月统计数量
     * 注：按公司
     * <br>
     *
     * @return
     * <AUTHOR>
     * @see [类、类#方法、类#成员]
     */
    @Query(value = "SELECT "
            + "SUM(MONTH(e.create_time) =1)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-01-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-01-01') AND (company_id = :companyId OR company_id IS NULL)),"
            + "SUM(MONTH(e.create_time) =2)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-02-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-02-01') AND (company_id = :companyId OR company_id IS NULL)),"
            + "SUM(MONTH(e.create_time) =3)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-03-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-03-01') AND (company_id = :companyId OR company_id IS NULL)),"
            + "SUM(MONTH(e.create_time) =4)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-04-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-04-01') AND (company_id = :companyId OR company_id IS NULL)),"
            + "SUM(MONTH(e.create_time) =5)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-05-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-05-01') AND (company_id = :companyId OR company_id IS NULL)),"
            + "SUM(MONTH(e.create_time) =6)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-06-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-06-01') AND (company_id = :companyId OR company_id IS NULL)),"
            + "SUM(MONTH(e.create_time) =7)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-07-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-07-01') AND (company_id = :companyId OR company_id IS NULL)),"
            + "SUM(MONTH(e.create_time) =8)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-08-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-08-01') AND (company_id = :companyId OR company_id IS NULL)),"
            + "SUM(MONTH(e.create_time) =9)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-09-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-09-01') AND (company_id = :companyId OR company_id IS NULL)),"
            + "SUM(MONTH(e.create_time) =10)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-10-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-10-01') AND (company_id = :companyId OR company_id IS NULL)),"
            + "SUM(MONTH(e.create_time) =11)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-11-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-11-01') AND (company_id = :companyId OR company_id IS NULL)),"
            + "SUM(MONTH(e.create_time) =12)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-12-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-12-01') AND (company_id = :companyId OR company_id IS NULL))"

            + "FROM emp_device e WHERE YEAR(e.create_time)= YEAR(:create_time) AND (e.company_id = :companyId OR company_id IS NULL)  ", nativeQuery = true)
    List<String> findDeviceByYearCompany(
            @Param("create_time") Date create_time,
            @Param("companyId") Long companyId);

    /**
     * 近一年按月统计数量
     * 注：按设备创建者
     * <br>
     *
     * @return
     * <AUTHOR>
     * @see [类、类#方法、类#成员]
     */
    @Query(value = "SELECT "
            + "SUM(MONTH(e.create_time) =1)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-01-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-01-01') AND (creator_id in :creatorIds )),"
            + "SUM(MONTH(e.create_time) =2)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-02-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-02-01') AND (creator_id in :creatorIds )),"
            + "SUM(MONTH(e.create_time) =3)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-03-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-03-01') AND (creator_id in :creatorIds )),"
            + "SUM(MONTH(e.create_time) =4)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-04-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-04-01') AND (creator_id in :creatorIds )),"
            + "SUM(MONTH(e.create_time) =5)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-05-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-05-01') AND (creator_id in :creatorIds )),"
            + "SUM(MONTH(e.create_time) =6)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-06-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-06-01') AND (creator_id in :creatorIds )),"
            + "SUM(MONTH(e.create_time) =7)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-07-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-07-01') AND (creator_id in :creatorIds )),"
            + "SUM(MONTH(e.create_time) =8)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-08-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-08-01') AND (creator_id in :creatorIds )),"
            + "SUM(MONTH(e.create_time) =9)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-09-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-09-01') AND (creator_id in :creatorIds )),"
            + "SUM(MONTH(e.create_time) =10)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-10-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-10-01') AND (creator_id in :creatorIds )),"
            + "SUM(MONTH(e.create_time) =11)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-11-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-11-01') AND (creator_id in :creatorIds )),"
            + "SUM(MONTH(e.create_time) =12)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-12-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-12-01') AND (creator_id in :creatorIds ))"

            + "FROM emp_device e WHERE YEAR(e.create_time)= YEAR(:create_time) AND (e.creator_id in :creatorIds )  ", nativeQuery = true)
    List<String> findDeviceByYearCreator(
            @Param("create_time") Date create_time,
            @Param("creatorIds") List<Long> creatorIds);


    /**
     * 近一年按月统计数量
     * 注：管理员
     * <br>
     *
     * @return
     * <AUTHOR>
     * @see [类、类#方法、类#成员]
     */
    @Query(value = "SELECT "
            + "SUM(MONTH(e.create_time) =1)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-01-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-01-01'))  ,"
            + "SUM(MONTH(e.create_time) =2)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-02-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-02-01'))  ,"
            + "SUM(MONTH(e.create_time) =3)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-03-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-03-01'))  ,"
            + "SUM(MONTH(e.create_time) =4)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-04-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-04-01'))  ,"
            + "SUM(MONTH(e.create_time) =5)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-05-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-05-01'))  ,"
            + "SUM(MONTH(e.create_time) =6)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-06-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-06-01'))  ,"
            + "SUM(MONTH(e.create_time) =7)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-07-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-07-01'))  ,"
            + "SUM(MONTH(e.create_time) =8)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-08-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-08-01'))  ,"
            + "SUM(MONTH(e.create_time) =9)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-09-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-09-01'))  ,"
            + "SUM(MONTH(e.create_time) =10)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-10-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-10-01'))  ,"
            + "SUM(MONTH(e.create_time) =11)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-11-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-11-01'))  ,"
            + "SUM(MONTH(e.create_time) =12)+(SELECT COUNT(*) FROM emp_device WHERE create_time<DATE_FORMAT(:create_time,'%Y-12-01') AND :create_time> DATE_FORMAT(:create_time,'%Y-12-01'))  "

            + "FROM emp_device e WHERE YEAR(e.create_time)= YEAR(:create_time) ", nativeQuery = true)
    List<String> findDeviceByYearAdmin(@Param("create_time") Date create_time);

    /**
     * 根据条件查询 想要级别的地址 ，如楼栋 单元 得到去重后的结果集
     *
     * @param communityId
     * @param levelName
     * @return
     */
    @Query(value = "SELECT new Device(buildingCode, unitCode, floorCode, roomCode)"
            + "FROM "
            + "Device  "
            + "WHERE "
            + "buildingCode IS NOT NULL "
            + " AND community = ?1 AND (companyId =?2 OR companyId IS NULL) "
            + "GROUP BY " + "buildingCode")
    List<Device> queryBuildingCodeByLevel(Community community, Long companyId);

    @Query(value = "SELECT new Device(buildingCode, unitCode, floorCode, roomCode)"
            + "FROM "
            + "Device  "
            + "WHERE "
            + "buildingCode IS NOT NULL "
            + " AND community = ?1 " + "GROUP BY " + "buildingCode")
    List<Device> queryBuildingCodeByLevelAdmin(Community community);

    @Query(value = "SELECT * "
            + "FROM "
            + "emp_device "
            + "WHERE "
            + " community_id = ?1 AND building_code = ?2  "
            + "AND unit_code IS NOT NULL AND (company_id =?3 OR company_id IS NULL)"
            + "GROUP BY " + "unit_code " + "UNION " + "SELECT *  FROM "
            + "emp_device  " + "WHERE "
            + "community_id = ?1 AND building_code = ?2 " + "AND ( "
            + "unit_code IS NULL " + "AND floor_code IS NOT NULL "
            + ") AND (company_id =?3 OR company_id IS NULL)" + "GROUP BY "
            + "floor_code " + "UNION " + "SELECT * FROM " + "emp_device  "
            + "WHERE " + "community_id = ?1 AND building_code = ?2 " + "AND ( "
            + "unit_code IS NULL " + "AND floor_code IS NULL "
            + "AND room_code IS NOT NULL "
            + ") AND (company_id =?3 OR company_id IS NULL)" + "GROUP BY "
            + "room_code;", nativeQuery = true)
    List<Device> queryUnitCodeByLevel(Integer communityId, String buildingCode,
                                      Long companyId);

    @Query(value = "SELECT * " + "FROM " + "emp_device " + "WHERE "
            + "community_id = ?1 AND building_code = ?2  "
            + "AND unit_code IS NOT NULL " + "GROUP BY " + "unit_code " + "UNION "
            + "SELECT *  FROM " + "emp_device  " + "WHERE "
            + "community_id = ?1 AND building_code = ?2 " + "AND ( "
            + "unit_code IS NULL " + "AND floor_code IS NOT NULL " + ") "
            + "GROUP BY " + "floor_code " + "UNION " + "SELECT * FROM "
            + "emp_device  " + "WHERE "
            + "community_id = ?1 AND building_code = ?2 " + "AND ( "
            + "unit_code IS NULL " + "AND floor_code IS NULL "
            + "AND room_code IS NOT NULL " + ") " + "GROUP BY " + "room_code;", nativeQuery = true)
    List<Device> queryUnitCodeByLevelAdmin(Integer communityId,
                                           String buildingCode);

    @Query(value = "SELECT * "
            + "FROM "
            + "emp_device  "
            + "WHERE "
            + "community_id = ?1 AND "
            + "building_code = ?2 AND unit_code = ?3 "
            + "AND floor_code IS NOT NULL AND (company_id =?4 OR company_id IS NULL)"
            + "GROUP BY floor_code "
            + "UNION "
            + "SELECT * FROM emp_device  "
            + "WHERE "
            + "community_id = ?1 AND "
            + "building_code = ?2 AND unit_code = ?3 AND floor_code IS  NULL "
            + "AND room_code IS NOT NULL AND (company_id =?4 OR company_id IS NULL)"
            + "GROUP BY room_code;", nativeQuery = true)
    List<Device> queryFloorCodeByLevel(Integer communityId,
                                       String buildingCode, String unitCode, Long companyId);

    @Query(value = "SELECT * " + "FROM " + "emp_device  " + "WHERE "
            + "community_id = ?1 AND " + "building_code = ?2 AND unit_code = ?3 "
            + "AND floor_code IS NOT NULL " + "GROUP BY floor_code " + "UNION "
            + "SELECT * FROM emp_device  " + "WHERE " + "community_id = ?1 AND "
            + "building_code = ?2 AND unit_code = ?3 AND floor_code IS  NULL "
            + "AND room_code IS NOT NULL " + "GROUP BY room_code;", nativeQuery = true)
    List<Device> queryFloorCodeByLevelAdmin(Integer communityId,
                                            String buildingCode, String unitCode);

    @Query(value = "SELECT  new Device(buildingCode,  unitCode,  floorCode, roomCode) FROM  Device  "
            + "WHERE "
            + " community = ?1 "
            + "AND buildingCode = ?2 "
            + "AND unitCode = ?3 "
            + "AND floorCode = ?4 "
            + "AND roomCode IS NOT NULL AND (companyId =?5 OR companyId IS NULL)"
            + "GROUP BY    floorCode")
    List<Device> queryRoomCodeByLevel(Community community, String buildingCode,
                                      String unitCode, String floorCode, Long CompanyId);

    @Query(value = "SELECT  new Device(buildingCode,  unitCode,  floorCode, roomCode) FROM  Device  "
            + "WHERE "
            + " community = ?1 "
            + "AND buildingCode = ?2 "
            + "AND unitCode = ?3 "
            + "AND floorCode = ?4 "
            + "AND roomCode IS NOT NULL " + "GROUP BY    floorCode")
    List<Device> queryRoomCodeByLevelAdmin(Community community,
                                           String buildingCode, String unitCode, String floorCode);

    @Query(value = "SELECT  new Device(buildingCode,  unitCode,  floorCode, roomCode) FROM  Device  "
            + "WHERE "
            + " community = ?1 "
            + "AND buildingCode = ?2 "
            + "AND unitCode IS NULL "
            + "AND floorCode = ?3 "
            + "AND roomCode IS NOT NULL AND (companyId =?4 OR companyId IS NULL)"
            + "GROUP BY    floorCode")
    List<Device> queryRoomCodeByLevel(Community community, String buildingCode,
                                      String floorCode, Long companyId);

    @Query(value = "SELECT  new Device(buildingCode,  unitCode,  floorCode, roomCode) FROM  Device  "
            + "WHERE "
            + " community = ?1 "
            + "AND buildingCode = ?2 "
            + "AND unitCode IS NULL "
            + "AND floorCode = ?3 "
            + "AND roomCode IS NOT NULL " + "GROUP BY    floorCode")
    List<Device> queryRoomCodeByLevelAdmin(Community community,
                                           String buildingCode, String floorCode);

    @Query(value = "SELECT * FROM emp_device e WHERE e.code = "
            + "(SELECT device_code  FROM emp_alarm WHERE id = (SELECT alarm_id FROM emp_task_alarm   WHERE id = :taskAlarmId))", nativeQuery = true)
    List<Device> pushDevice(@Param("taskAlarmId") Long taskAlarmId);

    @Query(value = "SELECT * FROM emp_device e WHERE e.code =:code ", nativeQuery = true)
    Device findByCode(@Param("code") String code);

    @Query("from Device d where d.creatorId in ?1 and d.projectId = ?2")
    List<Device> findByCreatorIds(@Param("creatorIds") List<Long> creatorIds,@Param("projectId")Long projectId);

    @Query("from Device as Device where Device.community.id = ?1")
    List<Device> findByCommunityId(@Param("communityId") Long communityId);

    @Query("from Device as Device  where (?1 is null or Device.code like '%'||?1||'%')  " +
            "and  (?2 is null or Device.projectId = ?2)  and (?3 is null or Device.community.id = ?3)  " +
            "and (?4 is null or Device.createTime > ?4) and (?5 is null or Device.createTime < ?5) " +
            "and (?6 is null or Device.isVirtualDevice = ?6) and (?7 is null or Device.isDeviceOn = ?7) " +
            "and (?8 is null or Device.isAutoCharged = ?8) and (?9 is null or Device.status = ?9)" +
            " and (?10 is null or Device.workFeeDate > ?10) and (?11 is null or Device.workFeeDate < ?11)")
    Page<Device> findAllByNameAndCommunityIdAndCreateTimeBetween(String code, Long projectId, Long communityId, Date startDt, Date endDt, Byte isVirtualDevice, Byte isDeviceOn, Byte isAutoCharged, Integer status, Date workFeeDateBegin, Date workFeeDateEnd, Pageable pageable);

    @Query("from Device as Device where (?1 is null or Device.code like '%'||?1||'%')  " +
            "and  (?2 is null or Device.projectId = ?2)  and (?3 is null or Device.community.id = ?3) " +
            "and Device.creatorId in (?4) and (?5 is null or Device.createTime > ?5) " +
            "and (?6 is null or Device.createTime < ?6) and (?7 is null or Device.isVirtualDevice = ?7) " +
            "and (?8 is null or Device.isDeviceOn = ?8) and (?9 is null or Device.isAutoCharged = ?9) and (?10 is null or Device.status = ?10) " +
            "and (?11 is null or Device.workFeeDate > ?11) and (?12 is null or Device.workFeeDate < ?12)")
    Page<Device> findAllByNameAndCommunityIdAndCreateTimeBetweenAAndCreatorIdIn(String code, Long projectId, Long communityId, List<Long> ids, Date startDt, Date endDt, Byte isVirtualDevice, Byte isDeviceOn, Byte isAutoCharged, Integer status, Date workFeeDateBegin, Date workFeeDateEnd, Pageable pageable);

    @Query("from Device as Device  where (?1 is null or Device.code like '%'||?1||'%')  " +
            "and  (?2 is null or Device.projectId = ?2)  and (?3 is null or Device.community.id = ?3) " +
            "and (?4 is null or Device.creatorId = ?4)  and (?5 is null or Device.createTime > ?5) " +
            "and (?6 is null or Device.createTime < ?6) and (?7 is null or Device.isVirtualDevice = ?7) " +
            "and (?8 is null or Device.isDeviceOn = ?8) and (?9 is null or Device.isAutoCharged = ?9) and (?10 is null or Device.status = ?10) " +
            "and (?11 is null or Device.workFeeDate > ?11) and (?12 is null or Device.workFeeDate < ?12)")
    Page<Device> findAllByNameAndCommunityIdAndCreatorIdAndCreateTimeBetween(String code, Long projectId, Long communityId, Long creatorId, Date startDt, Date endDt, Byte isVirtualDevice, Byte isDeviceOn, Byte isAutoCharged, Integer status, Date workFeeDateBegin, Date workFeeDateEnd, Pageable pageable);

    @Query("from Device as Device  where (?1 is null or Device.code like '%'||?1||'%')  " +
            "and  (?2 is null or Device.projectId = ?2)  and (?3 is null or Device.community.id = ?3) " +
            "and  Device.projectId in (?4) and (?5 is null or Device.createTime > ?5) " +
            "and (?6 is null or Device.createTime < ?6) and (?7 is null or Device.isVirtualDevice = ?7) " +
            "and (?8 is null or Device.isDeviceOn = ?8) and (?9 is null or Device.isAutoCharged = ?9) and (?10 is null or Device.status = ?10) " +
            "and (?11 is null or Device.workFeeDate > ?11) and (?12 is null or Device.workFeeDate < ?12)")
    Page<Device> findAllByNameAndCommunityIdAndCreateTimeBetweenInProjectIds(String code, Long projectId, Long communityId, List<Long> projectIds, Date startDt, Date endDt, Byte isVirtualDevice, Byte isDeviceOn, Byte isAutoCharged, Integer status, Date workFeeDateBegin, Date workFeeDateEnd, Pageable pageable);


    @Query("select code from Device where creatorId in (?1)")
    List<String> findDeviceCodeByCreatorIds(List<Long> creatorIds);

    @Query("select code from Device where projectId in (?1)")
    List<String> findDeviceCodeByProjectIds(List<Long> projectIds);


    @Query("select count(code) from Device where creatorId in (?1)")
    Long countAllByUserIds(List<Long> creatorIds);


    List<Device> findAllByDeviceUnitAndCreatorIdAndIsVirtualDevice(DeviceUnit deviceUnit, Long creatorId, Byte isVirtualDevice);

    List<Device> findAllByDeviceUnitAndCreatorIdAndIsVirtualDeviceAndProjectId(DeviceUnit deviceUnit, Long creatorId, Byte isVirtualDevice, Long projectId);

    @Query("from Device as Device  where (?1 is null or Device.code like '%'||?1||'%')  and  (?2 is null or Device.projectId = ?2)  and (?3 is null or Device.community.id = ?3)  and (?4 is null or Device.createTime > ?4) and (?5 is null or Device.createTime < ?5) and (?6 is null or Device.isVirtualDevice = ?6)")
    List<Device> findAllByNameAndCommunityIdAndCreateTimeBetweenNoPage(String code, Long projectId, Long communityId, Date startDt, Date endDt, Byte isVirtualDevice);

    @Query("from Device as Device where (?1 is null or Device.code like '%'||?1||'%')  and  (?2 is null or Device.projectId = ?2)  and (?3 is null or Device.community.id = ?3) and Device.creatorId in (?4) and (?5 is null or Device.createTime > ?5) and (?6 is null or Device.createTime < ?6) and (?7 is null or Device.isVirtualDevice = ?7)")
    List<Device> findAllByNameAndCommunityIdAndCreateTimeBetweenAndCreatorIdInNoPage(String code, Long projectId, Long communityId, List<Long> ids, Date startDt, Date endDt, Byte isVirtualDevice);

    @Query("from Device as Device  where (?1 is null or Device.code like '%'||?1||'%')  and  (?2 is null or Device.projectId = ?2)  and (?3 is null or Device.community.id = ?3) and (?4 is null or Device.creatorId = ?4)  and (?5 is null or Device.createTime > ?5) and (?6 is null or Device.createTime < ?6) and (?7 is null or Device.isVirtualDevice = ?7)")
    List<Device> findAllByNameAndCommunityIdAndCreatorIdAndCreateTimeBetweenNoPage(String code, Long projectId, Long communityId, Long creatorId, Date startDt, Date endDt, Byte isVirtualDevice);

    @Modifying
    @Query("update Device set nbDeviceId = :nbDeviceId where code = :code")
    void updateDevice(@Param("nbDeviceId") String nbDeviceId, @Param("code") String code);

    @Query("from Device as Device where Device.projectId = ?1 and (?2 is null or Device.community.id = ?2) order by Device.modifyTime desc")
    Page<Device> findByProjectIdAndCommunityId(@Param("projectId") Long projectId, @Param("communityId") Long communityId, Pageable pageable);
    
    @Query("select distinct Device.deviceUnit from Device as Device where Device.projectId = ?1")
    List<DeviceUnit> findDeviceUnitByProjectId(@Param("projectId") Long projectId);

    @Modifying
    @Query(value = "delete  from Device where projectId = ?1 and isVirtualDevice = 1 ")
    void deleteAllByProjectIdAndiIsVirtualDevice(Long projectId);

    @Query("from Device as Device where (?1 is null or Device.workFeeDate > ?1) and (?2 is null or Device.workFeeDate < ?2) " +
            "and ((?3 is null) or (?3 < 0 and Device.status > 0) or (?3 >= 0 and Device.status = ?3)) and (?4 is null or Device.isAutoCharged = ?4) and (?5 is null or Device.isDeviceOn = ?5)")
    List<Device> outdatedDevice(Date workFeeDateBegin, Date workFeeDateEnd, Integer status, Byte isAutoCharged, Byte isDeviceOn);


    @Query(value = "select status,count(1) from emp_device where creator_id in ?1 group by status ",nativeQuery = true)
    List<Object[]> countByStatusAndCreatorId(List<Long> userId);


    @Query(value = "select DATEDIFF(work_fee_date,?2) from emp_device where creator_id in ?1", nativeQuery = true)
    List<BigInteger> findExpireDevice(List<Long> userId, String date);



    @Query("from Device as Device where (coalesce(?1,null) is null or Device.deviceUnit.id in ?1)")
    List<Device> findAllGatewayDevices(List<Long> deviceUnitIds);

    @Modifying
    @Query("update Device d set d.name = ?1 where d.code = ?2")
    void updateNameByCode(@Param("name") String name, @Param("code") String code);

    @Query("from Device d where d.projectId = ?1")
    List<Device> findAllByProjectId(@Param("projectId") Long id);
}
