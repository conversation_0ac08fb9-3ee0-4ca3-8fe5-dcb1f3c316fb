package com.easylinkin.emp.constant;

/**
 * 本地日志枚举（模块ID）
 *
 * <AUTHOR>
 * @see com.easylinkin.services.log.LogEnum
 */
public enum LocalLogEnum {

    // LocalLogEnum 从81L开始

    /**
     * 示例
     */
    //    DEMO1(81L),
    //    DEMO2(82L);

    AREA(81L),

    INFOTYPE(82L),

    /**
     * 消息中心
     */
    INFOCENTER(83L),

    /**
     * 数据查询
     */
    DATAQUERY(84L),

    /**
     * 设备
     */
    DEVICE(90L),

    /**
     * 巡查报告
     */
    PATROL(100L),

    /**
     * 第三方施工巡查
     */
    PATROL_CONSTRUCTION(110L),

    /**
     * 巡查计划
     */
    PATROL_PLAN(11001L),

    /**
     * 渣土车监控
     */
    MUCKCARMONITOR(88L),
	
    /**
     * 人工识别
     */
	MANUALREC(80002L);

    public final Long modelId;

    LocalLogEnum(long code) {
        this.modelId = code;
    }
}
